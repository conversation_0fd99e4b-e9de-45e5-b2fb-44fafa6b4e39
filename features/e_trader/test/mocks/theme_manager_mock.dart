import 'package:flutter/material.dart';
import 'package:mocktail/mocktail.dart';
import 'package:theme_manager/theme_manager.dart';

class ThemeManagerMock extends Mock implements ThemeManager {
  bool _isDarkMode = false;

  @override
  bool get isDarkMode => _isDarkMode;

  @override
  bool hasUserSetTheme() {
    return true; // Default to true for tests
  }

  @override
  Future<void> loadThemeWithSystemFallback(BuildContext context) async {
    // Do nothing in tests
    return;
  }

  /// Method to set the dark mode for testing purposes
  void setDarkMode(bool darkMode) {
    _isDarkMode = darkMode;
  }

  // Override equality methods
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ThemeManagerMock && other._isDarkMode == _isDarkMode;
  }

  @override
  int get hashCode => _isDarkMode.hashCode;
}
