import 'package:equiti_test/equiti_test.dart';
import 'trade_options_test_data.dart';
import 'package:e_trader/fusion.dart';
import 'scenarios/trade_options_success_scenario.dart';

Feature: TradeOptions Golden Tests
@testMethodName: testGoldens
Scenario: User views trade options bottom sheet
  Given The {TradeOptionsTestData()} app is rendered {scenarios:[tradeOptionsSuccessScenario]}
  And I tap {'Show Trade Options'} text
  And I wait
  Then screenshot verified {'trade_options_success_sheet'} with custom pump
