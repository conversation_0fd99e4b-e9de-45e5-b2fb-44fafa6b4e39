// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'trade_options_test_data.dart';
import 'package:e_trader/fusion.dart';
import 'scenarios/trade_options_success_scenario.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_widget_test/step/i_tap_text.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import './step/screenshot_verified_with_custom_pump.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''TradeOptions Golden Tests''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(String title, bool success,
        [List<String>? tags]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''User views trade options bottom sheet''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User views trade options bottom sheet''');
        await theAppIsRendered(tester, TradeOptionsTestData(),
            scenarios: [tradeOptionsSuccessScenario]);
        await iTapText(tester, 'Show Trade Options');
        await iWait(tester);
        await screenshotVerifiedWithCustomPump(
            tester, 'trade_options_success_sheet');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''User views trade options bottom sheet''',
          success,
        );
      }
    });
    testGoldens('''User views trade options bottom sheet in dark mode''',
        (tester) async {
      var success = true;
      try {
        await beforeEach(
            '''User views trade options bottom sheet in dark mode''');
        await theAppIsRendered(tester, TradeOptionsTestData(),
            scenarios: [tradeOptionsSuccessScenario]);
        await iTapText(tester, 'Show Trade Options');
        await iWait(tester);
        await screenshotVerifiedWithCustomPump(
            tester, 'trade_options_success_sheet');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''User views trade options bottom sheet in dark mode''',
          success,
        );
      }
    });
  });
}
