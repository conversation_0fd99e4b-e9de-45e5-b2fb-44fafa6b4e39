import 'package:equiti_test/equiti_test.dart';
import 'scenarios/wallet_details_empty.dart';
import 'leverage_instructions_test_data.dart';
import 'scenarios/wallet_details_failure.dart';

Feature: Leverage Instruction
@testMethodName: testGoldens
Scenario: Screenshot leverage instruction screen success
  Given The {LeverageInstructionsTestData()} app is rendered
 And I tap {'Click Here'} text
  And I wait
  Then screenshot verified {'leverage_instruction_success'} with custom pump

Scenario: close leverage instruction
    Given The {LeverageInstructionsTestData()} app is rendered
    And I wait
     And I tap {'Click Here'} text
  And I wait
    Then i tap {"close_leverage_instruction_bottom_sheet"} identifier
  Then i wait for ui
    Then {"close_leverage_instruction_bottom_sheet"} identifier is not present