// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'scenarios/wallet_details_empty.dart';
import 'leverage_instructions_test_data.dart';
import 'scenarios/wallet_details_failure.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_widget_test/step/i_tap_text.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import './step/screenshot_verified_with_custom_pump.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';
import './step/i_wait_for_ui.dart';
import './step/identifier_is_not_present.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Leverage Instruction''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Screenshot leverage instruction screen success''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach('''Screenshot leverage instruction screen success''');
        await theAppIsRendered(tester, LeverageInstructionsTestData());
        await iTapText(tester, 'Click Here');
        await iWait(tester);
        await screenshotVerifiedWithCustomPump(
          tester,
          'leverage_instruction_success',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Screenshot leverage instruction screen success''',
          success,
        );
      }
    });
    testWidgets('''close leverage instruction''', (tester) async {
      var success = true;
      try {
        await beforeEach('''close leverage instruction''');
        await theAppIsRendered(tester, LeverageInstructionsTestData());
        await iWait(tester);
        await iTapText(tester, 'Click Here');
        await iWait(tester);
        await iTapIdentifier(tester, "close_leverage_instruction_bottom_sheet");
        await iWaitForUi(tester);
        await identifierIsNotPresent(
          tester,
          "close_leverage_instruction_bottom_sheet",
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''close leverage instruction''', success);
      }
    });
  });
}
