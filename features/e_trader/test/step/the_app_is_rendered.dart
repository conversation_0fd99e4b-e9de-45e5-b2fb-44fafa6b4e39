import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:toastification/toastification.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../mocks/theme_manager_mock.dart';

/// Usage: The {WithdrawScreen(walletId: '123', isFromAccountScreen: false)} app is rendered {scenarios:[withdrawSuccessScenario]}
/// Usage with theme: The {WithdrawScreen(walletId: '123', isFromAccountScreen: false)} app is rendered {scenarios:[withdrawSuccessScenario], theme: 'dark'}
Future<void> theAppIsRendered(
  WidgetTester tester,
  Widget app, {
  List<VoidCallback> scenarios = const <VoidCallback>[],
  String? theme,
}) async {
  final locale =
      Platform.environment['APP_LOCALE'] == 'ar'
          ? const Locale('ar')
          : const Locale('en');

  // Determine theme from parameter or environment variable
  final isDarkMode =
      theme == 'dark' || Platform.environment['APP_THEME'] == "dark";
  final duploTheme =
      isDarkMode ? DuploThemeData.dark() : DuploThemeData.light();

  // Update the ThemeManager mock to reflect the current theme
  final themeManager = diContainer<ThemeManager>();
  if (themeManager is ThemeManagerMock) {
    // Always set the theme explicitly to ensure consistency
    themeManager.setDarkMode(isDarkMode);
  }

  return await AppTestConfigurator(
    tester: tester,
    app: DuploTheme(
      child: DuploTextStyles(
        locale: locale,
        child: ToastificationWrapper(
          child: MaterialApp(
            home: app,
            localizationsDelegates: EquitiLocalization.localizationsDelegates,
            supportedLocales: EquitiLocalization.supportedLocales,
            locale: locale,
          ),
        ),
      ),
      data: duploTheme,
    ),
    isGoldenTest: true,
    scenarios: scenarios,
    onInit: () async {
      VisibilityDetectorController.instance.updateInterval = Duration.zero;
      await EquitiLocalizationManager.initMock();
    },
  ).run();
}
