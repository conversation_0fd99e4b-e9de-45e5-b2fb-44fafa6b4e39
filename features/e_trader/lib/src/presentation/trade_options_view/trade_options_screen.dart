import 'dart:async';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/usecase/update_positions_use_case.dart';
import 'package:e_trader/src/presentation/trade_options_view/trade_option_view.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:monitoring/monitoring.dart';
import 'package:theme_manager/theme_manager.dart';

const String kModifyTradeBottomSheetRouteName = 'modify_trade_bottom_sheet';
const String kPartialCloseSheetRouteName = 'partial_close_sheet';

FutureOr<void> showTradeOptionsSheet({
  required BuildContext context,
  required PositionModel position,
  String? currency,
  required String platformName,
}) {
  return DuploSheet.showModalSheetV2(
    context,
    appBar: DuploAppBar(
      title: EquitiLocalization.of(context).trader_trade,
      automaticallyImplyLeading: false,
      duploAppBarTextAlign: DuploAppBarTextAlign.left,
      actions: [
        IconButton(
          icon:
              diContainer<ThemeManager>().isDarkMode
                  ? Assets.images.closeIc.svg(
                    colorFilter: ColorFilter.mode(
                      DuploTheme.of(context).foreground.fgSecondary,
                      BlendMode.srcIn,
                    ),
                  )
                  : Assets.images.closeIc.svg(),
          onPressed: () => Navigator.pop(context),
        ),
      ],
    ),
    content: TradeOptionView(
      positionModel: position,
      currency: currency,
      platformName: platformName,
    ),
    bottomBar: SizedBox(height: 0, width: 0),
    onPopInvoked: (didPop, result) {
      if (didPop) {
        try {
          diContainer<UpdatePositionsUseCase>().call(
            positionId: int.parse(position.positionId),
            eventType: TradingSocketEvent.positions.unsubscribe,
            symbolName: platformName,
          );
        } catch (e) {
          diContainer<LoggerBase>().logError(e, stackTrace: StackTrace.current);
        }
      }
    },
  );
}
